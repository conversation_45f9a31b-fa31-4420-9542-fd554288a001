{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "typecheck": "nuxt typecheck --logLevel=verbose", "typecheck:content": "vue-tsc --noEmit", "docker:build": "docker run -p 3000:3000 nextssp-web-host"}, "dependencies": {"@nuxt/content": "^3.6.3", "@nuxt/eslint": "^1.9.0", "@nuxt/image": "^1.11.0", "@nuxt/scripts": "^0.11.13", "@nuxt/test-utils": "^3.19.2", "@nuxt/ui": "^3.3.2", "@unhead/vue": "^2.0.14", "eslint": "^9.34.0", "nuxt": "^4.0.3", "vue": "^3.5.20", "vue-router": "^4.5.1"}, "devDependencies": {"typescript": "^5.9.2", "vite": "6.3.5", "vue-tsc": "^3.0.6"}, "version": "0.0.33"}