# Build stage
## Use a base Node.js image
FROM node:22-alpine AS builder
## Set the working directory inside the container
WORKDIR /app
## Copy package.json and package-lock.json to leverage <PERSON><PERSON>'s build cache
COPY package*.json ./
## Install dependencies (including dev dependencies if needed for build)
RUN npm install
## Copy the rest of your application files
COPY . .
## Run the build command
RUN npm run build 
## Expose the port your application will run on (if applicable)
EXPOSE 4200
## Define the command to run your application when the container starts
CMD ["npm", "start"] 

# Production stage
## Use a base Node.js image
FROM node:22-alpine
## Set the working directory inside the container
WORKDIR /app
## Copy from builder stage to production stage
COPY --from=builder /app/dist ./dist # Copy built assets
## Copy package.json to production stage
COPY package.json ./
RUN npm install --production # Install only production dependencies
CMD ["node", "dist/index.js"] # Adjust based on your build output